---
source: src/config.rs
expression: config
---
Config {
    repos: [
        Remote(
            RemoteRepo {
                repo: Url {
                    scheme: "https",
                    cannot_be_a_base: false,
                    username: "",
                    password: None,
                    host: Some(
                        Domain(
                            "github.com",
                        ),
                    ),
                    port: None,
                    path: "/abravalheri/validate-pyproject",
                    query: None,
                    fragment: None,
                },
                rev: "v0.20.2",
                hooks: [
                    RemoteHook {
                        id: "validate-pyproject",
                        name: None,
                        entry: None,
                        language: None,
                        options: HookOptions {
                            alias: None,
                            files: None,
                            exclude: None,
                            types: None,
                            types_or: None,
                            exclude_types: None,
                            additional_dependencies: None,
                            args: None,
                            always_run: None,
                            fail_fast: None,
                            pass_filenames: None,
                            description: None,
                            language_version: None,
                            log_file: None,
                            require_serial: None,
                            stages: None,
                            verbose: None,
                            minimum_prek_version: None,
                        },
                    },
                ],
            },
        ),
        Remote(
            RemoteRepo {
                repo: Url {
                    scheme: "https",
                    cannot_be_a_base: false,
                    username: "",
                    password: None,
                    host: Some(
                        Domain(
                            "github.com",
                        ),
                    ),
                    port: None,
                    path: "/crate-ci/typos",
                    query: None,
                    fragment: None,
                },
                rev: "v1.26.0",
                hooks: [
                    RemoteHook {
                        id: "typos",
                        name: None,
                        entry: None,
                        language: None,
                        options: HookOptions {
                            alias: None,
                            files: None,
                            exclude: None,
                            types: None,
                            types_or: None,
                            exclude_types: None,
                            additional_dependencies: None,
                            args: None,
                            always_run: None,
                            fail_fast: None,
                            pass_filenames: None,
                            description: None,
                            language_version: None,
                            log_file: None,
                            require_serial: None,
                            stages: None,
                            verbose: None,
                            minimum_prek_version: None,
                        },
                    },
                ],
            },
        ),
        Local(
            LocalRepo {
                hooks: [
                    ManifestHook {
                        id: "cargo-fmt",
                        name: "cargo fmt",
                        entry: "cargo fmt --",
                        language: System,
                        options: HookOptions {
                            alias: None,
                            files: None,
                            exclude: None,
                            types: Some(
                                [
                                    "rust",
                                ],
                            ),
                            types_or: None,
                            exclude_types: None,
                            additional_dependencies: None,
                            args: None,
                            always_run: None,
                            fail_fast: None,
                            pass_filenames: Some(
                                false,
                            ),
                            description: None,
                            language_version: None,
                            log_file: None,
                            require_serial: None,
                            stages: None,
                            verbose: None,
                            minimum_prek_version: None,
                        },
                    },
                ],
            },
        ),
        Local(
            LocalRepo {
                hooks: [
                    ManifestHook {
                        id: "cargo-dev-generate-all",
                        name: "cargo dev generate-all",
                        entry: "cargo dev generate-all",
                        language: System,
                        options: HookOptions {
                            alias: None,
                            files: Some(
                                SerdeRegex(
                                    "^crates/(uv-cli|uv-settings)/",
                                ),
                            ),
                            exclude: None,
                            types: Some(
                                [
                                    "rust",
                                ],
                            ),
                            types_or: None,
                            exclude_types: None,
                            additional_dependencies: None,
                            args: None,
                            always_run: None,
                            fail_fast: None,
                            pass_filenames: Some(
                                false,
                            ),
                            description: None,
                            language_version: None,
                            log_file: None,
                            require_serial: None,
                            stages: None,
                            verbose: None,
                            minimum_prek_version: None,
                        },
                    },
                ],
            },
        ),
        Remote(
            RemoteRepo {
                repo: Url {
                    scheme: "https",
                    cannot_be_a_base: false,
                    username: "",
                    password: None,
                    host: Some(
                        Domain(
                            "github.com",
                        ),
                    ),
                    port: None,
                    path: "/pre-commit/mirrors-prettier",
                    query: None,
                    fragment: None,
                },
                rev: "v3.1.0",
                hooks: [
                    RemoteHook {
                        id: "prettier",
                        name: None,
                        entry: None,
                        language: None,
                        options: HookOptions {
                            alias: None,
                            files: None,
                            exclude: None,
                            types: None,
                            types_or: Some(
                                [
                                    "yaml",
                                    "json5",
                                ],
                            ),
                            exclude_types: None,
                            additional_dependencies: None,
                            args: None,
                            always_run: None,
                            fail_fast: None,
                            pass_filenames: None,
                            description: None,
                            language_version: None,
                            log_file: None,
                            require_serial: None,
                            stages: None,
                            verbose: None,
                            minimum_prek_version: None,
                        },
                    },
                ],
            },
        ),
        Remote(
            RemoteRepo {
                repo: Url {
                    scheme: "https",
                    cannot_be_a_base: false,
                    username: "",
                    password: None,
                    host: Some(
                        Domain(
                            "github.com",
                        ),
                    ),
                    port: None,
                    path: "/astral-sh/ruff-pre-commit",
                    query: None,
                    fragment: None,
                },
                rev: "v0.6.9",
                hooks: [
                    RemoteHook {
                        id: "ruff-format",
                        name: None,
                        entry: None,
                        language: None,
                        options: HookOptions {
                            alias: None,
                            files: None,
                            exclude: None,
                            types: None,
                            types_or: None,
                            exclude_types: None,
                            additional_dependencies: None,
                            args: None,
                            always_run: None,
                            fail_fast: None,
                            pass_filenames: None,
                            description: None,
                            language_version: None,
                            log_file: None,
                            require_serial: None,
                            stages: None,
                            verbose: None,
                            minimum_prek_version: None,
                        },
                    },
                    RemoteHook {
                        id: "ruff",
                        name: None,
                        entry: None,
                        language: None,
                        options: HookOptions {
                            alias: None,
                            files: None,
                            exclude: None,
                            types: None,
                            types_or: None,
                            exclude_types: None,
                            additional_dependencies: None,
                            args: Some(
                                [
                                    "--fix",
                                    "--exit-non-zero-on-fix",
                                ],
                            ),
                            always_run: None,
                            fail_fast: None,
                            pass_filenames: None,
                            description: None,
                            language_version: None,
                            log_file: None,
                            require_serial: None,
                            stages: None,
                            verbose: None,
                            minimum_prek_version: None,
                        },
                    },
                ],
            },
        ),
    ],
    default_install_hook_types: None,
    default_language_version: None,
    default_stages: None,
    files: None,
    exclude: Some(
        SerdeRegex(
            "^(.*/(snapshots)/.*|)$",
        ),
    ),
    fail_fast: Some(
        true,
    ),
    minimum_prek_version: None,
    ci: None,
}
