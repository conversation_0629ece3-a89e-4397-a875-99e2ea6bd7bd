[project]
name = "prek"
version = "0.0.24"
description = "Better `pre-commit`, re-engineered in Rust"
authors = [{ name = "j178", email = "<EMAIL>" }]
requires-python = ">=3.8"
keywords = [ "pre-commit", "git", "hooks" ]
readme = "README.md"
license = { file = "LICENSE" }
classifiers = [
  "Development Status :: 2 - Pre-Alpha",
  "Environment :: Console",
  "Intended Audience :: Developers",
  "Operating System :: OS Independent",
  "License :: OSI Approved :: MIT License",
  "Programming Language :: Rust",
  "Topic :: Software Development :: Quality Assurance"
]

[project.urls]
Repository = "https://github.com/j178/prek"
Changelog = "https://github.com/j178/prek/blob/main/CHANGELOG.md"
Releases = "https://github.com/j178/prek/releases"

[build-system]
requires = ["maturin>=1.0,<2.0"]
build-backend = "maturin"

[tool.maturin]
bindings = "bin"
include = [
  { path = "licenses/*", format = ["wheel", "sdist"]}
]

[tool.rooster]
version_tag_prefix = "v"
major_labels = []  # We do not use the major version number yet
minor_labels = ["breaking"]
changelog_ignore_labels = ["internal", "ci", "testing"]
changelog_sections.breaking = "Breaking changes"
changelog_sections.enhancement = "Enhancements"
changelog_sections.compatibility = "Enhancements"
changelog_sections.performance = "Performance"
changelog_sections.bug = "Bug fixes"
changelog_sections.documentation = "Documentation"
changelog_sections.__unknown__ = "Other changes"
changelog_contributors = true

version_files = [
  "README.md",
  "Cargo.toml",
]

[tool.uv]
managed = false
