fail_fast: true

exclude: |
  (?x)^(
    .*/(snapshots)/.*|
  )$

repos:
  - repo: https://github.com/abravalheri/validate-pyproject
    rev: v0.20.2
    hooks:
      - id: validate-pyproject

  - repo: https://github.com/crate-ci/typos
    rev: v1.26.0
    hooks:
      - id: typos

  - repo: local
    hooks:
      - id: cargo-fmt
        name: cargo fmt
        entry: cargo fmt --
        language: system
        types: [rust]
        pass_filenames: false # This makes it a lot faster

  - repo: local
    hooks:
      - id: cargo-dev-generate-all
        name: cargo dev generate-all
        entry: cargo dev generate-all
        language: system
        types: [rust]
        pass_filenames: false
        files: ^crates/(uv-cli|uv-settings)/

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        types_or: [yaml, json5]

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.6.9
    hooks:
      - id: ruff-format
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
