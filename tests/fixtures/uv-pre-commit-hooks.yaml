- id: pip-compile
  name: pip-compile
  description: "Automatically run 'uv pip compile' on your requirements"
  entry: uv pip compile
  language: python
  files: ^requirements\.(in|txt)$
  args: []
  pass_filenames: false
  additional_dependencies: []
  minimum_pre_commit_version: "2.9.2"
- id: uv-lock
  name: uv-lock
  description: "Automatically run 'uv lock' on your project dependencies"
  entry: uv lock
  language: python
  files: ^(uv\.lock|pyproject\.toml|uv\.toml)$
  args: []
  pass_filenames: false
  additional_dependencies: []
  minimum_pre_commit_version: "2.9.2"
- id: uv-export
  name: uv-export
  description: "Automatically run 'uv export' on your project dependencies"
  entry: uv export
  language: python
  files: ^uv\.lock$
  args: ["--frozen", "--output-file=requirements.txt"]
  pass_filenames: false
  additional_dependencies: []
  minimum_pre_commit_version: "2.9.2"
