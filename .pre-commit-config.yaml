fail_fast: true

exclude: |
  (?x)^(
    .*/(snapshots)/.*|
  )$

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer

  - repo: https://github.com/crate-ci/typos
    rev: v1.26.0
    hooks:
      - id: typos

  - repo: local
    hooks:
      - id: cargo-fmt
        name: cargo fmt
        entry: cargo fmt --
        language: system
        types: [rust]
        pass_filenames: false # This makes it a lot faster

  - repo: local
    hooks:
      - id: cargo-clippy
        name: cargo clippy
        language: system
        types: [rust]
        pass_filenames: false
        entry: cargo clippy --all-targets --all-features -- -D warnings
